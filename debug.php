<?php
/**
 * ملف تشخيص المشاكل
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>تشخيص المشاكل - WIDDX OMS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .step { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <h1>🔍 تشخيص مشاكل WIDDX OMS</h1>";

// الخطوة 1: فحص ملف config.php
echo "<div class='step'>";
echo "<h3>1. فحص ملف الإعدادات</h3>";
try {
    if (file_exists('config/config.php')) {
        require_once 'config/config.php';
        echo "<div class='success'>✅ تم تحميل config.php بنجاح</div>";
        
        // فحص الثوابت المطلوبة
        $requiredConstants = ['DB_HOST', 'DB_USER', 'DB_NAME', 'SYSTEM_NAME'];
        foreach ($requiredConstants as $const) {
            if (defined($const)) {
                echo "<div class='success'>✅ الثابت {$const} معرف: " . constant($const) . "</div>";
            } else {
                echo "<div class='error'>❌ الثابت {$const} غير معرف</div>";
            }
        }
    } else {
        echo "<div class='error'>❌ ملف config.php غير موجود</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في تحميل config.php: " . $e->getMessage() . "</div>";
}
echo "</div>";

// الخطوة 2: فحص قاعدة البيانات
echo "<div class='step'>";
echo "<h3>2. فحص قاعدة البيانات</h3>";
try {
    if (file_exists('config/database_unified.php')) {
        require_once 'config/database_unified.php';
        echo "<div class='success'>✅ تم تحميل database_unified.php بنجاح</div>";
        
        // اختبار الاتصال
        $db = getDatabase();
        if ($db && $db->isConnected()) {
            echo "<div class='success'>✅ الاتصال بقاعدة البيانات نجح</div>";
            
            // فحص الجداول
            $tables = ['customers', 'orders', 'order_items'];
            foreach ($tables as $table) {
                try {
                    $db->query("SHOW TABLES LIKE '{$table}'");
                    $result = $db->single();
                    if ($result) {
                        echo "<div class='success'>✅ الجدول {$table} موجود</div>";
                    } else {
                        echo "<div class='warning'>⚠️ الجدول {$table} غير موجود</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='error'>❌ خطأ في فحص الجدول {$table}: " . $e->getMessage() . "</div>";
                }
            }
        } else {
            echo "<div class='error'>❌ فشل الاتصال بقاعدة البيانات</div>";
        }
    } else {
        echo "<div class='error'>❌ ملف database_unified.php غير موجود</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في قاعدة البيانات: " . $e->getMessage() . "</div>";
}
echo "</div>";

// الخطوة 3: فحص الكلاسات
echo "<div class='step'>";
echo "<h3>3. فحص الكلاسات</h3>";
$classes = ['Customer', 'Order', 'WIDDXDatabase'];
foreach ($classes as $className) {
    if (class_exists($className)) {
        echo "<div class='success'>✅ الكلاس {$className} محمل</div>";
    } else {
        echo "<div class='warning'>⚠️ الكلاس {$className} غير محمل</div>";
        
        // محاولة تحميل الكلاس يدوياً
        $classFile = "classes/{$className}.php";
        if (file_exists($classFile)) {
            try {
                require_once $classFile;
                echo "<div class='info'>ℹ️ تم تحميل {$className} يدوياً</div>";
            } catch (Exception $e) {
                echo "<div class='error'>❌ خطأ في تحميل {$className}: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='error'>❌ ملف {$classFile} غير موجود</div>";
        }
    }
}
echo "</div>";

// الخطوة 4: فحص الملفات الأساسية
echo "<div class='step'>";
echo "<h3>4. فحص الملفات الأساسية</h3>";
$files = [
    'includes/header.php' => 'Header',
    'includes/footer.php' => 'Footer', 
    'includes/layout.php' => 'Layout',
    'assets/css/main.css' => 'CSS الرئيسي',
    'assets/js/main.js' => 'JavaScript الرئيسي'
];

foreach ($files as $file => $name) {
    if (file_exists($file)) {
        $size = filesize($file);
        echo "<div class='success'>✅ {$name}: موجود (" . number_format($size) . " بايت)</div>";
    } else {
        echo "<div class='error'>❌ {$name}: غير موجود ({$file})</div>";
    }
}
echo "</div>";

// الخطوة 5: اختبار Layout
echo "<div class='step'>";
echo "<h3>5. اختبار Layout</h3>";
try {
    if (file_exists('includes/layout.php')) {
        require_once 'includes/layout.php';
        echo "<div class='success'>✅ تم تحميل layout.php بنجاح</div>";
        
        // اختبار إنشاء Layout
        $layout = createLayout();
        if ($layout) {
            echo "<div class='success'>✅ تم إنشاء مثيل Layout بنجاح</div>";
        } else {
            echo "<div class='error'>❌ فشل في إنشاء مثيل Layout</div>";
        }
    } else {
        echo "<div class='error'>❌ ملف layout.php غير موجود</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في Layout: " . $e->getMessage() . "</div>";
}
echo "</div>";

// الخطوة 6: معلومات النظام
echo "<div class='step'>";
echo "<h3>6. معلومات النظام</h3>";
echo "<div class='info'>ℹ️ إصدار PHP: " . PHP_VERSION . "</div>";
echo "<div class='info'>ℹ️ نظام التشغيل: " . PHP_OS . "</div>";
echo "<div class='info'>ℹ️ الذاكرة المتاحة: " . ini_get('memory_limit') . "</div>";
echo "<div class='info'>ℹ️ وقت التنفيذ الأقصى: " . ini_get('max_execution_time') . " ثانية</div>";
echo "</div>";

echo "<div class='step info'>";
echo "<h3>📋 التوصيات</h3>";
echo "<p>إذا كانت جميع الفحوصات ناجحة، جرب:</p>";
echo "<ul>";
echo "<li>افتح <a href='index.php'>index.php</a> مرة أخرى</li>";
echo "<li>تحقق من console المتصفح للأخطاء</li>";
echo "<li>امسح cache المتصفح</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
