<?php
/**
 * صفحة رئيسية مبسطة للاختبار
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>WIDDX OMS - اختبار</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css' rel='stylesheet'>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }
        .hero { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 3rem 0; }
        .card { border: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-radius: 15px; }
        .stats-card { transition: transform 0.3s ease; }
        .stats-card:hover { transform: translateY(-5px); }
    </style>
</head>
<body>";

echo "<div class='hero text-center'>
    <div class='container'>
        <h1><i class='fas fa-box me-3'></i>WIDDX OMS</h1>
        <p class='lead'>نظام إدارة الطلبيات</p>
    </div>
</div>";

echo "<div class='container mt-4'>";

// اختبار تحميل النظام
echo "<div class='row mb-4'>";
echo "<div class='col-12'>";
echo "<div class='card'>";
echo "<div class='card-header'><h5><i class='fas fa-cogs'></i> حالة النظام</h5></div>";
echo "<div class='card-body'>";

try {
    echo "<div class='alert alert-info'><i class='fas fa-info-circle'></i> جاري فحص النظام...</div>";
    
    // تحميل الإعدادات
    if (file_exists('config/config.php')) {
        require_once 'config/config.php';
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم تحميل الإعدادات بنجاح</div>";
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> ملف الإعدادات غير موجود</div>";
    }
    
    // تحميل قاعدة البيانات
    if (file_exists('config/database_unified.php')) {
        require_once 'config/database_unified.php';
        echo "<div class='alert alert-success'><i class='fas fa-check'></i> تم تحميل قاعدة البيانات بنجاح</div>";
        
        // اختبار الاتصال
        $db = getDatabase();
        if ($db && $db->isConnected()) {
            echo "<div class='alert alert-success'><i class='fas fa-check'></i> الاتصال بقاعدة البيانات نجح</div>";
            
            // جلب إحصائيات بسيطة
            try {
                $db->query("SELECT COUNT(*) as count FROM customers");
                $customerCount = $db->single()['count'] ?? 0;
                
                $db->query("SELECT COUNT(*) as count FROM orders");
                $orderCount = $db->single()['count'] ?? 0;
                
                echo "<div class='alert alert-info'><i class='fas fa-chart-bar'></i> العملاء: {$customerCount} | الطلبيات: {$orderCount}</div>";
                
            } catch (Exception $e) {
                echo "<div class='alert alert-warning'><i class='fas fa-exclamation-triangle'></i> تعذر جلب الإحصائيات: " . $e->getMessage() . "</div>";
            }
        } else {
            echo "<div class='alert alert-danger'><i class='fas fa-times'></i> فشل الاتصال بقاعدة البيانات</div>";
        }
    } else {
        echo "<div class='alert alert-danger'><i class='fas fa-times'></i> ملف قاعدة البيانات غير موجود</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'><i class='fas fa-times'></i> خطأ: " . $e->getMessage() . "</div>";
}

echo "</div></div></div></div>";

// الإجراءات السريعة
echo "<div class='row mb-4'>";

$actions = [
    ['title' => 'إضافة عميل', 'url' => 'add_customer.php', 'icon' => 'fas fa-user-plus', 'color' => 'primary'],
    ['title' => 'إضافة طلبية', 'url' => 'add_order.php', 'icon' => 'fas fa-plus-circle', 'color' => 'success'],
    ['title' => 'عرض الطلبيات', 'url' => 'view_orders.php', 'icon' => 'fas fa-list', 'color' => 'info'],
    ['title' => 'إدارة العملاء', 'url' => 'manage_customers.php', 'icon' => 'fas fa-users-cog', 'color' => 'warning']
];

foreach ($actions as $action) {
    echo "<div class='col-md-3 mb-3'>";
    echo "<div class='card stats-card h-100'>";
    echo "<div class='card-body text-center'>";
    echo "<i class='{$action['icon']} fa-2x text-{$action['color']} mb-3'></i>";
    echo "<h5>{$action['title']}</h5>";
    echo "<a href='{$action['url']}' class='btn btn-{$action['color']}'>{$action['title']}</a>";
    echo "</div></div></div>";
}

echo "</div>";

// روابط التشخيص
echo "<div class='row'>";
echo "<div class='col-12'>";
echo "<div class='card'>";
echo "<div class='card-header'><h5><i class='fas fa-tools'></i> أدوات التشخيص</h5></div>";
echo "<div class='card-body text-center'>";
echo "<a href='debug.php' class='btn btn-outline-primary me-2'><i class='fas fa-bug'></i> تشخيص شامل</a>";
echo "<a href='test_fixes.php' class='btn btn-outline-secondary me-2'><i class='fas fa-check-circle'></i> اختبار الإصلاحات</a>";
echo "<a href='system_test.php' class='btn btn-outline-info'><i class='fas fa-cogs'></i> اختبار النظام</a>";
echo "</div></div></div></div>";

echo "</div>";

echo "<script src='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js'></script>";
echo "<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('✅ Simple index loaded successfully');
    
    // اختبار تحميل الملفات
    const testFiles = [
        'assets/css/main.css',
        'assets/js/main.js',
        'assets/js/widdx-core.js'
    ];
    
    testFiles.forEach(file => {
        fetch(file, {method: 'HEAD'})
            .then(response => {
                if (response.ok) {
                    console.log('✅ File exists:', file);
                } else {
                    console.warn('⚠️ File not found:', file);
                }
            })
            .catch(error => {
                console.error('❌ Error checking file:', file, error);
            });
    });
});
</script>";

echo "</body></html>";
?>
