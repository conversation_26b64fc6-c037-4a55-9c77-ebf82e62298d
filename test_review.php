<?php
/**
 * ملف اختبار شامل للمراجعة
 * يفحص جميع المكونات الأساسية للنظام
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html lang='ar' dir='rtl'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>مراجعة شاملة - WIDDX OMS</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; background: #f8f9fa; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { color: #28a745; background: #d4edda; padding: 10px; margin: 5px 0; border-radius: 5px; border: 1px solid #c3e6cb; }";
echo ".error { color: #dc3545; background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 5px; border: 1px solid #f5c6cb; }";
echo ".warning { color: #856404; background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 5px; border: 1px solid #ffeaa7; }";
echo ".info { color: #0c5460; background: #d1ecf1; padding: 10px; margin: 5px 0; border-radius: 5px; border: 1px solid #bee5eb; }";
echo ".step { margin: 20px 0; padding: 15px; border: 1px solid #dee2e6; border-radius: 8px; }";
echo "h1 { color: #495057; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #6c757d; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }";
echo "h3 { color: #495057; margin-top: 20px; }";
echo ".summary { background: #e7f3ff; padding: 15px; border-radius: 8px; margin: 20px 0; border: 1px solid #b8daff; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🔍 مراجعة شاملة لنظام WIDDX OMS</h1>";

$allTestsPassed = true;
$testResults = [];

// 1. فحص ملفات التكوين
echo "<div class='step'>";
echo "<h2>1. فحص ملفات التكوين</h2>";

$configFiles = [
    'config/config.php' => 'ملف الإعدادات الرئيسي',
    'config/database_unified.php' => 'قاعدة البيانات الموحدة',
    'config/autoload.php' => 'التحميل التلقائي'
];

$configTestPassed = true;
foreach ($configFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ {$description}: موجود</div>";
        
        // فحص محتوى الملف
        $content = file_get_contents($file);
        if (strpos($content, '<?php') === 0) {
            echo "<div class='info'>ℹ️ {$description}: صيغة PHP صحيحة</div>";
        } else {
            echo "<div class='warning'>⚠️ {$description}: قد لا يحتوي على صيغة PHP صحيحة</div>";
        }
    } else {
        echo "<div class='error'>❌ {$description}: غير موجود ({$file})</div>";
        $configTestPassed = false;
        $allTestsPassed = false;
    }
}
$testResults['config'] = $configTestPassed;
echo "</div>";

// 2. فحص الكلاسات
echo "<div class='step'>";
echo "<h2>2. فحص الكلاسات</h2>";

// تحميل ملفات التكوين
if (file_exists('config/config.php')) {
    require_once 'config/config.php';
    echo "<div class='success'>✅ تم تحميل config.php</div>";
}

if (file_exists('config/database_unified.php')) {
    require_once 'config/database_unified.php';
    echo "<div class='success'>✅ تم تحميل database_unified.php</div>";
}

if (file_exists('config/autoload.php')) {
    require_once 'config/autoload.php';
    echo "<div class='success'>✅ تم تحميل autoload.php</div>";
}

$classes = [
    'WIDDXDatabase' => 'كلاس قاعدة البيانات الموحد',
    'Customer' => 'كلاس العملاء',
    'Order' => 'كلاس الطلبيات'
];

$classesTestPassed = true;
foreach ($classes as $className => $description) {
    if (class_exists($className)) {
        echo "<div class='success'>✅ {$description}: محمل بنجاح</div>";
        
        // اختبار إنشاء مثيل
        try {
            if ($className === 'WIDDXDatabase') {
                $instance = WIDDXDatabase::getInstance();
            } else {
                $instance = new $className();
            }
            echo "<div class='info'>ℹ️ {$description}: يمكن إنشاء مثيل منه</div>";
        } catch (Exception $e) {
            echo "<div class='warning'>⚠️ {$description}: خطأ في إنشاء مثيل - {$e->getMessage()}</div>";
        }
    } else {
        echo "<div class='error'>❌ {$description}: غير محمل</div>";
        $classesTestPassed = false;
        $allTestsPassed = false;
    }
}
$testResults['classes'] = $classesTestPassed;
echo "</div>";

// 3. فحص ملفات JavaScript
echo "<div class='step'>";
echo "<h2>3. فحص ملفات JavaScript</h2>";

$jsFiles = [
    'assets/js/main.js' => 'JavaScript الرئيسي',
    'assets/js/widdx-core.js' => 'JavaScript الأساسي',
    'sw.js' => 'Service Worker'
];

$jsTestPassed = true;
foreach ($jsFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ {$description}: موجود</div>";
        
        $content = file_get_contents($file);
        $lines = count(explode("\n", $content));
        echo "<div class='info'>ℹ️ {$description}: {$lines} سطر</div>";
        
        // فحص بعض الوظائف المهمة
        if ($file === 'assets/js/widdx-core.js') {
            if (strpos($content, 'updateQuickStats') !== false) {
                echo "<div class='success'>✅ وظيفة updateQuickStats موجودة</div>";
            } else {
                echo "<div class='warning'>⚠️ وظيفة updateQuickStats غير موجودة</div>";
            }
        }
    } else {
        echo "<div class='error'>❌ {$description}: غير موجود ({$file})</div>";
        $jsTestPassed = false;
        $allTestsPassed = false;
    }
}
$testResults['javascript'] = $jsTestPassed;
echo "</div>";

// 4. فحص ملفات CSS
echo "<div class='step'>";
echo "<h2>4. فحص ملفات CSS</h2>";

$cssFiles = [
    'assets/css/main.css' => 'CSS الرئيسي',
    'assets/css/components.css' => 'CSS المكونات'
];

$cssTestPassed = true;
foreach ($cssFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ {$description}: موجود</div>";
        
        $content = file_get_contents($file);
        $lines = count(explode("\n", $content));
        echo "<div class='info'>ℹ️ {$description}: {$lines} سطر</div>";
        
        // فحص المتغيرات
        if (strpos($content, ':root') !== false) {
            echo "<div class='success'>✅ يحتوي على متغيرات CSS</div>";
        }
    } else {
        echo "<div class='error'>❌ {$description}: غير موجود ({$file})</div>";
        $cssTestPassed = false;
        $allTestsPassed = false;
    }
}
$testResults['css'] = $cssTestPassed;
echo "</div>";

// 5. فحص ملفات API
echo "<div class='step'>";
echo "<h2>5. فحص ملفات API</h2>";

$apiFiles = [
    'api/quick_stats.php' => 'API الإحصائيات السريعة',
    'api/customers.php' => 'API العملاء',
    'api/orders.php' => 'API الطلبيات'
];

$apiTestPassed = true;
foreach ($apiFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ {$description}: موجود</div>";
    } else {
        echo "<div class='warning'>⚠️ {$description}: غير موجود ({$file})</div>";
        // API files are not critical for basic functionality
    }
}
$testResults['api'] = $apiTestPassed;
echo "</div>";

// 6. ملخص النتائج
echo "<div class='summary'>";
echo "<h2>📊 ملخص نتائج المراجعة</h2>";

$passedTests = array_sum($testResults);
$totalTests = count($testResults);

if ($allTestsPassed) {
    echo "<div class='success'>";
    echo "<h3>🎉 تهانينا! جميع الاختبارات نجحت</h3>";
    echo "<p>النظام جاهز للاستخدام بدون مشاكل.</p>";
    echo "</div>";
} else {
    echo "<div class='warning'>";
    echo "<h3>⚠️ هناك بعض المشاكل التي تحتاج إلى إصلاح</h3>";
    echo "<p>نجح {$passedTests} من أصل {$totalTests} اختبارات.</p>";
    echo "</div>";
}

echo "<h3>تفاصيل النتائج:</h3>";
foreach ($testResults as $test => $result) {
    $status = $result ? "✅ نجح" : "❌ فشل";
    $testNames = [
        'config' => 'ملفات التكوين',
        'classes' => 'الكلاسات',
        'javascript' => 'ملفات JavaScript',
        'css' => 'ملفات CSS',
        'api' => 'ملفات API'
    ];
    echo "<div class='" . ($result ? 'success' : 'error') . "'>";
    echo "{$status} {$testNames[$test]}";
    echo "</div>";
}

echo "</div>";

echo "<div class='info'>";
echo "<h3>📝 ملاحظات إضافية:</h3>";
echo "<ul>";
echo "<li>تم حل تعارض وظائف updateQuickStats</li>";
echo "<li>تم توحيد استخدام قاعدة البيانات</li>";
echo "<li>تم إنشاء Service Worker</li>";
echo "<li>تم إصلاح API الإحصائيات السريعة</li>";
echo "<li>تم حذف التعريفات المكررة في CSS</li>";
echo "</ul>";
echo "</div>";

echo "</div>";
echo "</body>";
echo "</html>";
?>
