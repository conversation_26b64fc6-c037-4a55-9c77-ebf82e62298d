<?php
/**
 * API محسن للإحصائيات السريعة
 * يوفر إحصائيات سريعة وإشعارات للعرض في شريط التنقل
 */

// إعداد الرؤوس
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

// تحميل النظام
require_once __DIR__ . '/../config/autoload.php';

try {
    // التحقق من طريقة الطلب
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        throw new Exception('Method not allowed');
    }

    // إنشاء مثيلات من الكلاسات
    $db = getDatabase();

    // التحقق من وجود الجداول المطلوبة
    $db->query("SHOW TABLES LIKE 'orders'");
    $ordersTable = $db->single();

    $db->query("SHOW TABLES LIKE 'customers'");
    $customersTable = $db->single();

    if (!$ordersTable || !$customersTable) {
        throw new Exception('Required database tables not found. Please run database setup.');
    }

    // إحصائيات اليوم
    $today = date('Y-m-d');
    $thisWeek = date('Y-m-d', strtotime('-7 days'));
    $thisMonth = date('Y-m-d', strtotime('-30 days'));

    // استعلام منفصل للعملاء الجدد اليوم
    $db->query("SELECT COUNT(*) as today_customers FROM customers WHERE DATE(created_at) = :today_customers");
    $db->bind(':today_customers', $today);
    $customerStats = $db->single();
    $todayCustomers = (int)($customerStats['today_customers'] ?? 0);

    // استعلام موحد للإحصائيات الأخرى
    $db->query("
        SELECT
            -- طلبيات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today_orders THEN 1 ELSE 0 END) as today_orders,

            -- مبيعات اليوم
            SUM(CASE WHEN DATE(o.created_at) = :today_sales AND o.status = 'completed' THEN o.total_amount ELSE 0 END) as today_sales,

            -- الطلبيات المعلقة
            SUM(CASE WHEN o.status = 'pending' THEN 1 ELSE 0 END) as pending_orders,

            -- الطلبيات قيد التنفيذ
            SUM(CASE WHEN o.status = 'processing' THEN 1 ELSE 0 END) as processing_orders,

            -- الطلبيات المكتملة
            SUM(CASE WHEN o.status = 'completed' THEN 1 ELSE 0 END) as completed_orders,

            -- الطلبيات المتأخرة (أكثر من 3 أيام)
            SUM(CASE WHEN o.status IN ('pending', 'processing') AND o.created_at < DATE_SUB(NOW(), INTERVAL 3 DAY) THEN 1 ELSE 0 END) as overdue_orders,

            -- إحصائيات الأسبوع
            SUM(CASE WHEN DATE(o.created_at) >= :this_week THEN 1 ELSE 0 END) as week_orders,

            -- إحصائيات الشهر
            SUM(CASE WHEN DATE(o.created_at) >= :this_month THEN 1 ELSE 0 END) as month_orders,

            -- إجمالي المبيعات
            SUM(CASE WHEN o.status = 'completed' THEN o.total_amount ELSE 0 END) as total_sales

        FROM orders o
    ");
    $db->bind(':today_orders', $today);
    $db->bind(':today_sales', $today);
    $db->bind(':this_week', $thisWeek);
    $db->bind(':this_month', $thisMonth);
    $stats = $db->single();

    // تحويل القيم إلى أرقام صحيحة
    $todayOrders = (int)($stats['today_orders'] ?? 0);
    // $todayCustomers already set above from separate query
    $todaySales = (float)($stats['today_sales'] ?? 0);
    $pendingOrders = (int)($stats['pending_orders'] ?? 0);
    $processingOrders = (int)($stats['processing_orders'] ?? 0);
    $completedOrders = (int)($stats['completed_orders'] ?? 0);
    $overdueOrders = (int)($stats['overdue_orders'] ?? 0);
    $weekOrders = (int)($stats['week_orders'] ?? 0);
    $monthOrders = (int)($stats['month_orders'] ?? 0);
    $totalSales = (float)($stats['total_sales'] ?? 0);
    
    // إنشاء الإشعارات المحسنة
    $notifications = [];
    $notificationId = 1;

    // إشعارات الطلبيات المتأخرة (أولوية عالية)
    if ($overdueOrders > 0) {
        $notifications[] = [
            'id' => $notificationId++,
            'type' => 'danger',
            'title' => 'طلبيات متأخرة',
            'message' => "لديك {$overdueOrders} طلبية متأخرة تحتاج لمتابعة عاجلة",
            'url' => 'view_orders.php?overdue=1',
            'icon' => 'fas fa-exclamation-triangle',
            'time' => 'الآن',
            'read' => false,
            'priority' => 'high'
        ];
    }

    // إشعارات الطلبيات المعلقة
    if ($pendingOrders > 0) {
        $notifications[] = [
            'id' => $notificationId++,
            'type' => 'warning',
            'title' => 'طلبيات معلقة',
            'message' => "لديك {$pendingOrders} طلبية معلقة تحتاج للمراجعة",
            'url' => 'view_orders.php?status=pending',
            'icon' => 'fas fa-clock',
            'time' => 'منذ دقائق',
            'read' => false,
            'priority' => 'medium'
        ];
    }

    // إشعارات الطلبيات الجديدة
    if ($todayOrders > 0) {
        $notifications[] = [
            'id' => $notificationId++,
            'type' => 'info',
            'title' => 'طلبيات جديدة',
            'message' => "تم إضافة {$todayOrders} طلبية جديدة اليوم",
            'url' => 'view_orders.php?date=' . $today,
            'icon' => 'fas fa-plus-circle',
            'time' => 'اليوم',
            'read' => false,
            'priority' => 'low'
        ];
    }

    // إشعار العملاء الجدد
    if ($todayCustomers > 0) {
        $notifications[] = [
            'id' => $notificationId++,
            'type' => 'success',
            'title' => 'عملاء جدد',
            'message' => "انضم {$todayCustomers} عميل جديد اليوم",
            'url' => 'manage_customers.php?new=1',
            'icon' => 'fas fa-user-plus',
            'time' => 'اليوم',
            'read' => false,
            'priority' => 'low'
        ];
    }

    // حساب معدل الإنجاز
    $totalActiveOrders = $pendingOrders + $processingOrders + $completedOrders;
    $completionRate = $totalActiveOrders > 0 ? round(($completedOrders / $totalActiveOrders) * 100, 1) : 0;

    // إعداد الاستجابة المحسنة
    $response = [
        'success' => true,
        'stats' => [
            'today_orders' => $todayOrders,
            'today_customers' => $todayCustomers,
            'today_sales' => number_format($todaySales, 0),
            'pending_orders' => $pendingOrders,
            'processing_orders' => $processingOrders,
            'completed_orders' => $completedOrders,
            'overdue_orders' => $overdueOrders,
            'week_orders' => $weekOrders,
            'month_orders' => $monthOrders,
            'total_sales' => number_format($totalSales, 0),
            'completion_rate' => $completionRate
        ],
        'notifications' => $notifications,
        'summary' => [
            'total_notifications' => count($notifications),
            'high_priority' => count(array_filter($notifications, fn($n) => $n['priority'] === 'high')),
            'unread_count' => count(array_filter($notifications, fn($n) => !$n['read']))
        ],
        'timestamp' => time(),
        'server_time' => date('Y-m-d H:i:s'),
        'cache_duration' => 60 // ثانية
    ];
    
    // إرسال الاستجابة
    echo json_encode($response, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

} catch (PDOException $e) {
    // خطأ في قاعدة البيانات
    $response = [
        'success' => false,
        'message' => 'خطأ في الاتصال بقاعدة البيانات',
        'error_code' => 'DB_ERROR',
        'error' => $e->getMessage(), // إظهار الخطأ للتشخيص
        'timestamp' => time(),
        'file' => __FILE__,
        'line' => $e->getLine()
    ];

    http_response_code(500);
    echo json_encode($response, JSON_UNESCAPED_UNICODE);

    // تسجيل الخطأ
    error_log("Quick stats DB error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

} catch (Exception $e) {
    // خطأ عام
    $response = [
        'success' => false,
        'message' => 'حدث خطأ في جلب الإحصائيات',
        'error_code' => 'GENERAL_ERROR',
        'error' => defined('DEBUG_MODE') && DEBUG_MODE ? $e->getMessage() : null,
        'timestamp' => time()
    ];

    // تحديد رمز الاستجابة بناءً على نوع الخطأ
    if (strpos($e->getMessage(), 'Method not allowed') !== false) {
        http_response_code(405);
    } else {
        http_response_code(500);
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);

    // تسجيل الخطأ
    error_log("Quick stats API error: " . $e->getMessage());

} finally {
    // تنظيف الموارد
    if (isset($db)) {
        $db = null;
    }
}
?>
