<?php
/**
 * ملف التحميل التلقائي للكلاسات
 * يقوم بتحميل الكلاسات تلقائياً عند الحاجة إليها
 */

// تحميل الإعدادات أولاً
require_once __DIR__ . '/config.php';

// تحميل قاعدة البيانات الموحدة
require_once __DIR__ . '/database_unified.php';

/**
 * وظيفة التحميل التلقائي للكلاسات
 */
spl_autoload_register(function ($className) {
    // مسارات البحث عن الكلاسات
    $paths = [
        CLASSES_PATH . '/' . $className . '.php',
        __DIR__ . '/../classes/' . $className . '.php',
        __DIR__ . '/../includes/' . $className . '.php'
    ];
    
    // البحث عن الكلاس في المسارات المختلفة
    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
    
    // إذا لم يتم العثور على الكلاس، سجل خطأ
    error_log("Class not found: {$className}");
});

/**
 * تحميل الملفات الأساسية
 */
function loadCoreFiles() {
    $coreFiles = [
        __DIR__ . '/../classes/Database.php',
        __DIR__ . '/../classes/Customer.php',
        __DIR__ . '/../classes/Order.php'
    ];

    foreach ($coreFiles as $file) {
        if (file_exists($file)) {
            require_once $file;
        }
    }
}

// تحميل الملفات الأساسية
loadCoreFiles();

/**
 * تهيئة النظام
 */
function initializeSystem() {
    // التحقق من وجود المجلدات المطلوبة
    $requiredDirs = [
        UPLOADS_PATH,
        LOGS_PATH,
        ASSETS_PATH . '/css',
        ASSETS_PATH . '/js',
        ASSETS_PATH . '/images'
    ];
    
    foreach ($requiredDirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    // إنشاء ملف .htaccess لحماية مجلد logs
    $htaccessContent = "Order Deny,Allow\nDeny from all";
    $htaccessPath = LOGS_PATH . '/.htaccess';
    
    if (!file_exists($htaccessPath)) {
        file_put_contents($htaccessPath, $htaccessContent);
    }
    
    // إنشاء ملف index.php فارغ في المجلدات الحساسة
    $protectedDirs = [LOGS_PATH, UPLOADS_PATH];
    foreach ($protectedDirs as $dir) {
        $indexFile = $dir . '/index.php';
        if (!file_exists($indexFile)) {
            file_put_contents($indexFile, '<?php // Access denied');
        }
    }
}

// تهيئة النظام
initializeSystem();

/**
 * معالج الأخطاء المخصص
 */
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    $errorTypes = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Notice',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];
    
    $errorType = $errorTypes[$errno] ?? 'Unknown Error';
    $message = "[{$errorType}] {$errstr} in {$errfile} on line {$errline}";
    
    error_log($message);
    
    // في وضع التطوير، اعرض الخطأ
    if (DEBUG_MODE) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 5px;'>";
        echo "<strong>{$errorType}:</strong> {$errstr}<br>";
        echo "<small>File: {$errfile} | Line: {$errline}</small>";
        echo "</div>";
    }
    
    return true;
}

// تسجيل معالج الأخطاء
set_error_handler('customErrorHandler');

/**
 * معالج الاستثناءات المخصص
 */
function customExceptionHandler($exception) {
    $message = "Uncaught Exception: " . $exception->getMessage() . 
               " in " . $exception->getFile() . 
               " on line " . $exception->getLine();
    
    error_log($message);
    
    if (DEBUG_MODE) {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 5px;'>";
        echo "<strong>Uncaught Exception:</strong> " . $exception->getMessage() . "<br>";
        echo "<small>File: " . $exception->getFile() . " | Line: " . $exception->getLine() . "</small>";
        echo "<pre>" . $exception->getTraceAsString() . "</pre>";
        echo "</div>";
    } else {
        echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 5px;'>";
        echo "<strong>حدث خطأ في النظام.</strong> يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.";
        echo "</div>";
    }
}

// تسجيل معالج الاستثناءات
set_exception_handler('customExceptionHandler');

/**
 * وظيفة التنظيف عند إنهاء السكريبت
 */
function shutdownHandler() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
        $message = "Fatal Error: " . $error['message'] . 
                   " in " . $error['file'] . 
                   " on line " . $error['line'];
        error_log($message);
    }
}

// تسجيل معالج الإغلاق
register_shutdown_function('shutdownHandler');

/**
 * وظائف مساعدة للتطوير
 */
if (DEBUG_MODE) {
    /**
     * طباعة متغير بتنسيق جميل
     */
    function dd($var) {
        echo '<pre style="background: #f5f5f5; border: 1px solid #ddd; padding: 10px; margin: 10px; border-radius: 5px;">';
        var_dump($var);
        echo '</pre>';
        die();
    }
    
    /**
     * طباعة متغير دون إيقاف التنفيذ
     */
    function dump($var) {
        echo '<pre style="background: #f5f5f5; border: 1px solid #ddd; padding: 10px; margin: 10px; border-radius: 5px;">';
        var_dump($var);
        echo '</pre>';
    }
}

/**
 * تحميل إعدادات إضافية من ملفات منفصلة
 */
function loadAdditionalConfigs() {
    $configFiles = [
        __DIR__ . '/mail.php',
        __DIR__ . '/cache.php',
        __DIR__ . '/security.php'
    ];
    
    foreach ($configFiles as $file) {
        if (file_exists($file)) {
            require_once $file;
        }
    }
}

// تحميل الإعدادات الإضافية
loadAdditionalConfigs();

// تسجيل بداية تشغيل النظام
error_log("WIDDX OMS: System initialized successfully");
