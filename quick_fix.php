<?php
/**
 * إصلاح سريع لمشكلة "جاري التحميل"
 */

// تفعيل عرض الأخطاء
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>
<html lang='ar' dir='rtl'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>إصلاح سريع - WIDDX OMS</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .step { margin: 15px 0; padding: 15px; border-radius: 8px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
    </style>
</head>
<body>
    <div class='container'>
        <h1>🔧 إصلاح سريع لمشكلة التحميل</h1>";

$fixes = [];

// الإصلاح 1: فحص وإصلاح ملف autoload.php
echo "<div class='step'>";
echo "<h3>1. إصلاح ملف autoload.php</h3>";
try {
    $autoloadPath = 'config/autoload.php';
    if (file_exists($autoloadPath)) {
        $content = file_get_contents($autoloadPath);
        
        // فحص وجود Helper::
        if (strpos($content, 'Helper::') !== false) {
            echo "<div class='warning'>⚠️ وجدت مراجع لـ Helper class - سيتم إصلاحها</div>";
            
            // استبدال Helper:: بـ error_log
            $content = str_replace('Helper::logError(', 'error_log(', $content);
            $content = str_replace('Helper::createDirectoryIfNotExists($dir);', 
                'if (!is_dir($dir)) { mkdir($dir, 0755, true); }', $content);
            
            file_put_contents($autoloadPath, $content);
            echo "<div class='success'>✅ تم إصلاح ملف autoload.php</div>";
            $fixes[] = "إصلاح autoload.php";
        } else {
            echo "<div class='success'>✅ ملف autoload.php سليم</div>";
        }
    } else {
        echo "<div class='error'>❌ ملف autoload.php غير موجود</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في إصلاح autoload.php: " . $e->getMessage() . "</div>";
}
echo "</div>";

// الإصلاح 2: إنشاء ملف index.php مبسط
echo "<div class='step'>";
echo "<h3>2. إنشاء نسخة احتياطية من index.php</h3>";
try {
    if (file_exists('index.php')) {
        // إنشاء نسخة احتياطية
        copy('index.php', 'index_backup.php');
        echo "<div class='success'>✅ تم إنشاء نسخة احتياطية: index_backup.php</div>";
        
        // إنشاء index.php مبسط
        $simpleIndex = '<?php
/**
 * صفحة رئيسية مبسطة - WIDDX OMS
 */

// تفعيل عرض الأخطاء للتشخيص
error_reporting(E_ALL);
ini_set("display_errors", 1);

try {
    // تحميل الإعدادات الأساسية فقط
    require_once "config/config.php";
    require_once "config/database_unified.php";
    
    // اختبار قاعدة البيانات
    $db = getDatabase();
    $dbStatus = $db && $db->isConnected() ? "متصل" : "غير متصل";
    
} catch (Exception $e) {
    $dbStatus = "خطأ: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WIDDX OMS - نظام إدارة الطلبيات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body { font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif; }
        .hero { background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 3rem 0; }
        .card { border: none; box-shadow: 0 4px 6px rgba(0,0,0,0.1); border-radius: 15px; margin-bottom: 1.5rem; }
    </style>
</head>
<body>
    <div class="hero text-center">
        <div class="container">
            <h1><i class="fas fa-box me-3"></i>WIDDX OMS</h1>
            <p class="lead">نظام إدارة الطلبيات</p>
            <p>حالة قاعدة البيانات: <span class="badge bg-light text-dark"><?php echo $dbStatus; ?></span></p>
        </div>
    </div>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-user-plus fa-2x text-primary mb-3"></i>
                        <h5>إضافة عميل</h5>
                        <a href="add_customer.php" class="btn btn-primary">إضافة</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-plus-circle fa-2x text-success mb-3"></i>
                        <h5>إضافة طلبية</h5>
                        <a href="add_order.php" class="btn btn-success">إضافة</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-list fa-2x text-info mb-3"></i>
                        <h5>عرض الطلبيات</h5>
                        <a href="view_orders.php" class="btn btn-info">عرض</a>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-users-cog fa-2x text-warning mb-3"></i>
                        <h5>إدارة العملاء</h5>
                        <a href="manage_customers.php" class="btn btn-warning">إدارة</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> أدوات التشخيص</h5>
            </div>
            <div class="card-body text-center">
                <a href="debug.php" class="btn btn-outline-primary me-2">تشخيص شامل</a>
                <a href="simple_index.php" class="btn btn-outline-secondary me-2">الصفحة المبسطة</a>
                <a href="index_backup.php" class="btn btn-outline-info">النسخة الأصلية</a>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>';

        file_put_contents('index_simple.php', $simpleIndex);
        echo "<div class='success'>✅ تم إنشاء index_simple.php</div>";
        $fixes[] = "إنشاء صفحة رئيسية مبسطة";
        
    } else {
        echo "<div class='error'>❌ ملف index.php غير موجود</div>";
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ خطأ في إنشاء النسخة المبسطة: " . $e->getMessage() . "</div>";
}
echo "</div>";

// الإصلاح 3: فحص المجلدات المطلوبة
echo "<div class='step'>";
echo "<h3>3. فحص وإنشاء المجلدات المطلوبة</h3>";
$requiredDirs = ['logs', 'uploads', 'assets/css', 'assets/js', 'assets/images'];
foreach ($requiredDirs as $dir) {
    if (!is_dir($dir)) {
        try {
            mkdir($dir, 0755, true);
            echo "<div class='success'>✅ تم إنشاء المجلد: {$dir}</div>";
            $fixes[] = "إنشاء مجلد {$dir}";
        } catch (Exception $e) {
            echo "<div class='error'>❌ فشل في إنشاء المجلد {$dir}: " . $e->getMessage() . "</div>";
        }
    } else {
        echo "<div class='info'>ℹ️ المجلد موجود: {$dir}</div>";
    }
}
echo "</div>";

// الإصلاح 4: فحص الملفات الأساسية
echo "<div class='step'>";
echo "<h3>4. فحص الملفات الأساسية</h3>";
$requiredFiles = [
    'config/config.php' => 'ملف الإعدادات',
    'config/database_unified.php' => 'قاعدة البيانات',
    'includes/header.php' => 'Header',
    'includes/footer.php' => 'Footer'
];

foreach ($requiredFiles as $file => $name) {
    if (file_exists($file)) {
        echo "<div class='success'>✅ {$name}: موجود</div>";
    } else {
        echo "<div class='error'>❌ {$name}: غير موجود ({$file})</div>";
    }
}
echo "</div>";

// ملخص الإصلاحات
echo "<div class='step info'>";
echo "<h3>📋 ملخص الإصلاحات</h3>";
if (count($fixes) > 0) {
    echo "<ul>";
    foreach ($fixes as $fix) {
        echo "<li>✅ {$fix}</li>";
    }
    echo "</ul>";
    echo "<div class='alert alert-success mt-3'>";
    echo "<strong>تم تطبيق " . count($fixes) . " إصلاح.</strong><br>";
    echo "جرب الآن:";
    echo "<ul class='mt-2'>";
    echo "<li><a href='index_simple.php' class='btn btn-sm btn-primary'>الصفحة المبسطة</a></li>";
    echo "<li><a href='debug.php' class='btn btn-sm btn-secondary'>التشخيص الشامل</a></li>";
    echo "<li><a href='index.php' class='btn btn-sm btn-info'>الصفحة الأصلية</a></li>";
    echo "</ul>";
    echo "</div>";
} else {
    echo "<div class='alert alert-info'>";
    echo "لم يتم العثور على مشاكل تحتاج إصلاح.";
    echo "</div>";
}
echo "</div>";

echo "</div></body></html>";
?>
